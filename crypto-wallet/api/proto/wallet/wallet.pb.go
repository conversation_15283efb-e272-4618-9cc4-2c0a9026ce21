// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.1
// 	protoc        v3.12.4
// source: wallet.proto

package wallet

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Wallet represents a crypto wallet
type Wallet struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id        string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	UserId    string                 `protobuf:"bytes,2,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	Name      string                 `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	Address   string                 `protobuf:"bytes,4,opt,name=address,proto3" json:"address,omitempty"`
	Chain     string                 `protobuf:"bytes,5,opt,name=chain,proto3" json:"chain,omitempty"`
	Type      string                 `protobuf:"bytes,6,opt,name=type,proto3" json:"type,omitempty"`
	CreatedAt *timestamppb.Timestamp `protobuf:"bytes,7,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	UpdatedAt *timestamppb.Timestamp `protobuf:"bytes,8,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
}

func (x *Wallet) Reset() {
	*x = Wallet{}
	if protoimpl.UnsafeEnabled {
		mi := &file_wallet_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Wallet) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Wallet) ProtoMessage() {}

func (x *Wallet) ProtoReflect() protoreflect.Message {
	mi := &file_wallet_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Wallet.ProtoReflect.Descriptor instead.
func (*Wallet) Descriptor() ([]byte, []int) {
	return file_wallet_proto_rawDescGZIP(), []int{0}
}

func (x *Wallet) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *Wallet) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *Wallet) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Wallet) GetAddress() string {
	if x != nil {
		return x.Address
	}
	return ""
}

func (x *Wallet) GetChain() string {
	if x != nil {
		return x.Chain
	}
	return ""
}

func (x *Wallet) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *Wallet) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *Wallet) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

// GetWalletRequest represents a request to get a wallet
type GetWalletRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
}

func (x *GetWalletRequest) Reset() {
	*x = GetWalletRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_wallet_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetWalletRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetWalletRequest) ProtoMessage() {}

func (x *GetWalletRequest) ProtoReflect() protoreflect.Message {
	mi := &file_wallet_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetWalletRequest.ProtoReflect.Descriptor instead.
func (*GetWalletRequest) Descriptor() ([]byte, []int) {
	return file_wallet_proto_rawDescGZIP(), []int{1}
}

func (x *GetWalletRequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

// GetWalletResponse represents a response to a get wallet request
type GetWalletResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Wallet *Wallet `protobuf:"bytes,1,opt,name=wallet,proto3" json:"wallet,omitempty"`
}

func (x *GetWalletResponse) Reset() {
	*x = GetWalletResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_wallet_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetWalletResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetWalletResponse) ProtoMessage() {}

func (x *GetWalletResponse) ProtoReflect() protoreflect.Message {
	mi := &file_wallet_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetWalletResponse.ProtoReflect.Descriptor instead.
func (*GetWalletResponse) Descriptor() ([]byte, []int) {
	return file_wallet_proto_rawDescGZIP(), []int{2}
}

func (x *GetWalletResponse) GetWallet() *Wallet {
	if x != nil {
		return x.Wallet
	}
	return nil
}

// ListWalletsRequest represents a request to list wallets
type ListWalletsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserId string `protobuf:"bytes,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	Chain  string `protobuf:"bytes,2,opt,name=chain,proto3" json:"chain,omitempty"`
	Limit  int32  `protobuf:"varint,3,opt,name=limit,proto3" json:"limit,omitempty"`
	Offset int32  `protobuf:"varint,4,opt,name=offset,proto3" json:"offset,omitempty"`
}

func (x *ListWalletsRequest) Reset() {
	*x = ListWalletsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_wallet_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListWalletsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListWalletsRequest) ProtoMessage() {}

func (x *ListWalletsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_wallet_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListWalletsRequest.ProtoReflect.Descriptor instead.
func (*ListWalletsRequest) Descriptor() ([]byte, []int) {
	return file_wallet_proto_rawDescGZIP(), []int{3}
}

func (x *ListWalletsRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *ListWalletsRequest) GetChain() string {
	if x != nil {
		return x.Chain
	}
	return ""
}

func (x *ListWalletsRequest) GetLimit() int32 {
	if x != nil {
		return x.Limit
	}
	return 0
}

func (x *ListWalletsRequest) GetOffset() int32 {
	if x != nil {
		return x.Offset
	}
	return 0
}

// ListWalletsResponse represents a response to a list wallets request
type ListWalletsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Wallets []*Wallet `protobuf:"bytes,1,rep,name=wallets,proto3" json:"wallets,omitempty"`
	Total   int32     `protobuf:"varint,2,opt,name=total,proto3" json:"total,omitempty"`
}

func (x *ListWalletsResponse) Reset() {
	*x = ListWalletsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_wallet_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListWalletsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListWalletsResponse) ProtoMessage() {}

func (x *ListWalletsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_wallet_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListWalletsResponse.ProtoReflect.Descriptor instead.
func (*ListWalletsResponse) Descriptor() ([]byte, []int) {
	return file_wallet_proto_rawDescGZIP(), []int{4}
}

func (x *ListWalletsResponse) GetWallets() []*Wallet {
	if x != nil {
		return x.Wallets
	}
	return nil
}

func (x *ListWalletsResponse) GetTotal() int32 {
	if x != nil {
		return x.Total
	}
	return 0
}

// GetBalanceRequest represents a request to get wallet balance
type GetBalanceRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	WalletId     string `protobuf:"bytes,1,opt,name=wallet_id,json=walletId,proto3" json:"wallet_id,omitempty"`
	Token        string `protobuf:"bytes,2,opt,name=token,proto3" json:"token,omitempty"`
	TokenAddress string `protobuf:"bytes,3,opt,name=token_address,json=tokenAddress,proto3" json:"token_address,omitempty"`
}

func (x *GetBalanceRequest) Reset() {
	*x = GetBalanceRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_wallet_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetBalanceRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetBalanceRequest) ProtoMessage() {}

func (x *GetBalanceRequest) ProtoReflect() protoreflect.Message {
	mi := &file_wallet_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetBalanceRequest.ProtoReflect.Descriptor instead.
func (*GetBalanceRequest) Descriptor() ([]byte, []int) {
	return file_wallet_proto_rawDescGZIP(), []int{5}
}

func (x *GetBalanceRequest) GetWalletId() string {
	if x != nil {
		return x.WalletId
	}
	return ""
}

func (x *GetBalanceRequest) GetToken() string {
	if x != nil {
		return x.Token
	}
	return ""
}

func (x *GetBalanceRequest) GetTokenAddress() string {
	if x != nil {
		return x.TokenAddress
	}
	return ""
}

// GetBalanceResponse represents a response to a get balance request
type GetBalanceResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Balance      string `protobuf:"bytes,1,opt,name=balance,proto3" json:"balance,omitempty"`
	Token        string `protobuf:"bytes,2,opt,name=token,proto3" json:"token,omitempty"`
	Symbol       string `protobuf:"bytes,3,opt,name=symbol,proto3" json:"symbol,omitempty"`
	Decimals     int32  `protobuf:"varint,4,opt,name=decimals,proto3" json:"decimals,omitempty"`
	TokenAddress string `protobuf:"bytes,5,opt,name=token_address,json=tokenAddress,proto3" json:"token_address,omitempty"`
}

func (x *GetBalanceResponse) Reset() {
	*x = GetBalanceResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_wallet_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetBalanceResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetBalanceResponse) ProtoMessage() {}

func (x *GetBalanceResponse) ProtoReflect() protoreflect.Message {
	mi := &file_wallet_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetBalanceResponse.ProtoReflect.Descriptor instead.
func (*GetBalanceResponse) Descriptor() ([]byte, []int) {
	return file_wallet_proto_rawDescGZIP(), []int{6}
}

func (x *GetBalanceResponse) GetBalance() string {
	if x != nil {
		return x.Balance
	}
	return ""
}

func (x *GetBalanceResponse) GetToken() string {
	if x != nil {
		return x.Token
	}
	return ""
}

func (x *GetBalanceResponse) GetSymbol() string {
	if x != nil {
		return x.Symbol
	}
	return ""
}

func (x *GetBalanceResponse) GetDecimals() int32 {
	if x != nil {
		return x.Decimals
	}
	return 0
}

func (x *GetBalanceResponse) GetTokenAddress() string {
	if x != nil {
		return x.TokenAddress
	}
	return ""
}

// ImportWalletRequest represents a request to import a wallet
type ImportWalletRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserId     string `protobuf:"bytes,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	Name       string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	PrivateKey string `protobuf:"bytes,3,opt,name=private_key,json=privateKey,proto3" json:"private_key,omitempty"`
	Chain      string `protobuf:"bytes,4,opt,name=chain,proto3" json:"chain,omitempty"`
	Passphrase string `protobuf:"bytes,5,opt,name=passphrase,proto3" json:"passphrase,omitempty"`
}

func (x *ImportWalletRequest) Reset() {
	*x = ImportWalletRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_wallet_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ImportWalletRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ImportWalletRequest) ProtoMessage() {}

func (x *ImportWalletRequest) ProtoReflect() protoreflect.Message {
	mi := &file_wallet_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ImportWalletRequest.ProtoReflect.Descriptor instead.
func (*ImportWalletRequest) Descriptor() ([]byte, []int) {
	return file_wallet_proto_rawDescGZIP(), []int{7}
}

func (x *ImportWalletRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *ImportWalletRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ImportWalletRequest) GetPrivateKey() string {
	if x != nil {
		return x.PrivateKey
	}
	return ""
}

func (x *ImportWalletRequest) GetChain() string {
	if x != nil {
		return x.Chain
	}
	return ""
}

func (x *ImportWalletRequest) GetPassphrase() string {
	if x != nil {
		return x.Passphrase
	}
	return ""
}

// ImportWalletResponse represents a response to an import wallet request
type ImportWalletResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Wallet *Wallet `protobuf:"bytes,1,opt,name=wallet,proto3" json:"wallet,omitempty"`
}

func (x *ImportWalletResponse) Reset() {
	*x = ImportWalletResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_wallet_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ImportWalletResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ImportWalletResponse) ProtoMessage() {}

func (x *ImportWalletResponse) ProtoReflect() protoreflect.Message {
	mi := &file_wallet_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ImportWalletResponse.ProtoReflect.Descriptor instead.
func (*ImportWalletResponse) Descriptor() ([]byte, []int) {
	return file_wallet_proto_rawDescGZIP(), []int{8}
}

func (x *ImportWalletResponse) GetWallet() *Wallet {
	if x != nil {
		return x.Wallet
	}
	return nil
}

// ExportWalletRequest represents a request to export a wallet
type ExportWalletRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	WalletId   string `protobuf:"bytes,1,opt,name=wallet_id,json=walletId,proto3" json:"wallet_id,omitempty"`
	Passphrase string `protobuf:"bytes,2,opt,name=passphrase,proto3" json:"passphrase,omitempty"`
}

func (x *ExportWalletRequest) Reset() {
	*x = ExportWalletRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_wallet_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExportWalletRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExportWalletRequest) ProtoMessage() {}

func (x *ExportWalletRequest) ProtoReflect() protoreflect.Message {
	mi := &file_wallet_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExportWalletRequest.ProtoReflect.Descriptor instead.
func (*ExportWalletRequest) Descriptor() ([]byte, []int) {
	return file_wallet_proto_rawDescGZIP(), []int{9}
}

func (x *ExportWalletRequest) GetWalletId() string {
	if x != nil {
		return x.WalletId
	}
	return ""
}

func (x *ExportWalletRequest) GetPassphrase() string {
	if x != nil {
		return x.Passphrase
	}
	return ""
}

// ExportWalletResponse represents a response to an export wallet request
type ExportWalletResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PrivateKey string `protobuf:"bytes,1,opt,name=private_key,json=privateKey,proto3" json:"private_key,omitempty"`
	Keystore   string `protobuf:"bytes,2,opt,name=keystore,proto3" json:"keystore,omitempty"`
}

func (x *ExportWalletResponse) Reset() {
	*x = ExportWalletResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_wallet_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExportWalletResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExportWalletResponse) ProtoMessage() {}

func (x *ExportWalletResponse) ProtoReflect() protoreflect.Message {
	mi := &file_wallet_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExportWalletResponse.ProtoReflect.Descriptor instead.
func (*ExportWalletResponse) Descriptor() ([]byte, []int) {
	return file_wallet_proto_rawDescGZIP(), []int{10}
}

func (x *ExportWalletResponse) GetPrivateKey() string {
	if x != nil {
		return x.PrivateKey
	}
	return ""
}

func (x *ExportWalletResponse) GetKeystore() string {
	if x != nil {
		return x.Keystore
	}
	return ""
}

var file_wallet_proto_msgTypes = make([]protoimpl.MessageInfo, 11)
var file_wallet_proto_rawDescGZIP = func() []byte { return nil }
