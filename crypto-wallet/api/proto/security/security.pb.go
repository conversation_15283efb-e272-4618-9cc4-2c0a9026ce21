// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.1
// 	protoc        v3.12.4
// source: security.proto

package security

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// SecurityEvent represents a security event
type SecurityEvent struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id          string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Type        string                 `protobuf:"bytes,2,opt,name=type,proto3" json:"type,omitempty"`
	Severity    string                 `protobuf:"bytes,3,opt,name=severity,proto3" json:"severity,omitempty"`
	Description string                 `protobuf:"bytes,4,opt,name=description,proto3" json:"description,omitempty"`
	UserId      string                 `protobuf:"bytes,5,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	IpAddress   string                 `protobuf:"bytes,6,opt,name=ip_address,json=ipAddress,proto3" json:"ip_address,omitempty"`
	UserAgent   string                 `protobuf:"bytes,7,opt,name=user_agent,json=userAgent,proto3" json:"user_agent,omitempty"`
	CreatedAt   *timestamppb.Timestamp `protobuf:"bytes,8,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
}

func (x *SecurityEvent) Reset() {
	*x = SecurityEvent{}
	if protoimpl.UnsafeEnabled {
		mi := &file_security_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SecurityEvent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SecurityEvent) ProtoMessage() {}

func (x *SecurityEvent) ProtoReflect() protoreflect.Message {
	mi := &file_security_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SecurityEvent.ProtoReflect.Descriptor instead.
func (*SecurityEvent) Descriptor() ([]byte, []int) {
	return file_security_proto_rawDescGZIP(), []int{0}
}

func (x *SecurityEvent) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *SecurityEvent) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *SecurityEvent) GetSeverity() string {
	if x != nil {
		return x.Severity
	}
	return ""
}

func (x *SecurityEvent) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *SecurityEvent) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *SecurityEvent) GetIpAddress() string {
	if x != nil {
		return x.IpAddress
	}
	return ""
}

func (x *SecurityEvent) GetUserAgent() string {
	if x != nil {
		return x.UserAgent
	}
	return ""
}

func (x *SecurityEvent) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

// LogSecurityEventRequest represents a request to log a security event
type LogSecurityEventRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Type        string `protobuf:"bytes,1,opt,name=type,proto3" json:"type,omitempty"`
	Severity    string `protobuf:"bytes,2,opt,name=severity,proto3" json:"severity,omitempty"`
	Description string `protobuf:"bytes,3,opt,name=description,proto3" json:"description,omitempty"`
	UserId      string `protobuf:"bytes,4,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	IpAddress   string `protobuf:"bytes,5,opt,name=ip_address,json=ipAddress,proto3" json:"ip_address,omitempty"`
	UserAgent   string `protobuf:"bytes,6,opt,name=user_agent,json=userAgent,proto3" json:"user_agent,omitempty"`
}

func (x *LogSecurityEventRequest) Reset() {
	*x = LogSecurityEventRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_security_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LogSecurityEventRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LogSecurityEventRequest) ProtoMessage() {}

func (x *LogSecurityEventRequest) ProtoReflect() protoreflect.Message {
	mi := &file_security_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LogSecurityEventRequest.ProtoReflect.Descriptor instead.
func (*LogSecurityEventRequest) Descriptor() ([]byte, []int) {
	return file_security_proto_rawDescGZIP(), []int{1}
}

func (x *LogSecurityEventRequest) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *LogSecurityEventRequest) GetSeverity() string {
	if x != nil {
		return x.Severity
	}
	return ""
}

func (x *LogSecurityEventRequest) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *LogSecurityEventRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *LogSecurityEventRequest) GetIpAddress() string {
	if x != nil {
		return x.IpAddress
	}
	return ""
}

func (x *LogSecurityEventRequest) GetUserAgent() string {
	if x != nil {
		return x.UserAgent
	}
	return ""
}

// LogSecurityEventResponse represents a response to a log security event request
type LogSecurityEventResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Event *SecurityEvent `protobuf:"bytes,1,opt,name=event,proto3" json:"event,omitempty"`
}

func (x *LogSecurityEventResponse) Reset() {
	*x = LogSecurityEventResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_security_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LogSecurityEventResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LogSecurityEventResponse) ProtoMessage() {}

func (x *LogSecurityEventResponse) ProtoReflect() protoreflect.Message {
	mi := &file_security_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LogSecurityEventResponse.ProtoReflect.Descriptor instead.
func (*LogSecurityEventResponse) Descriptor() ([]byte, []int) {
	return file_security_proto_rawDescGZIP(), []int{2}
}

func (x *LogSecurityEventResponse) GetEvent() *SecurityEvent {
	if x != nil {
		return x.Event
	}
	return nil
}

// SecurityServiceClient is the client API for SecurityService service.
type SecurityServiceClient interface {
	// LogSecurityEvent logs a security event
	LogSecurityEvent(ctx context.Context, in *LogSecurityEventRequest, opts ...grpc.CallOption) (*LogSecurityEventResponse, error)
}

type securityServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewSecurityServiceClient(cc grpc.ClientConnInterface) SecurityServiceClient {
	return &securityServiceClient{cc}
}

func (c *securityServiceClient) LogSecurityEvent(ctx context.Context, in *LogSecurityEventRequest, opts ...grpc.CallOption) (*LogSecurityEventResponse, error) {
	out := new(LogSecurityEventResponse)
	err := c.cc.Invoke(ctx, "/security.SecurityService/LogSecurityEvent", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// SecurityServiceServer is the server API for SecurityService service.
type SecurityServiceServer interface {
	// LogSecurityEvent logs a security event
	LogSecurityEvent(context.Context, *LogSecurityEventRequest) (*LogSecurityEventResponse, error)
}

// UnimplementedSecurityServiceServer can be embedded to have forward compatible implementations.
type UnimplementedSecurityServiceServer struct {
}

func (UnimplementedSecurityServiceServer) LogSecurityEvent(context.Context, *LogSecurityEventRequest) (*LogSecurityEventResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method LogSecurityEvent not implemented")
}

func RegisterSecurityServiceServer(s grpc.ServiceRegistrar, srv SecurityServiceServer) {
	s.RegisterService(&SecurityService_ServiceDesc, srv)
}

func _SecurityService_LogSecurityEvent_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(LogSecurityEventRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SecurityServiceServer).LogSecurityEvent(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/security.SecurityService/LogSecurityEvent",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SecurityServiceServer).LogSecurityEvent(ctx, req.(*LogSecurityEventRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var SecurityService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "security.SecurityService",
	HandlerType: (*SecurityServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "LogSecurityEvent",
			Handler:    _SecurityService_LogSecurityEvent_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "security.proto",
}

var file_security_proto_msgTypes = make([]protoimpl.MessageInfo, 3)
var file_security_proto_rawDescGZIP = func() []byte { return nil }
